package main

import (
	"fmt"
	"log"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type Condition struct {
	Elements []string // 元素（可能是1个或4个）
	Columns  []int    // 对应的列
	Score    float64  // 得分
	Type     string   // "single" 或 "quad"
}

// 完整的元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "bz1": 1.45, "bz2": 4.40, "bz3": 4.70,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "dz1": 1.92, "dz2": 3.18, "dz3": 3.40,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
	"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
	"a31": 4.30, "a32": 6.25, "a33": 26.00,
	"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
	"b31": 8.15, "b32": 11.50, "b33": 34.00,
	"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
	"c31": 4.40, "c32": 6.50, "c33": 26.00,
	"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
	"d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算得分
func calculateScore(elements []string) float64 {
	if len(elements) == 1 {
		if score, exists := ElementScores[elements[0]]; exists {
			return 100.0 / score
		}
	} else {
		product := 1.0
		for _, element := range elements {
			if score, exists := ElementScores[element]; exists {
				product *= score
			} else {
				return 0
			}
		}
		return 100.0 / product
	}
	return 0
}

// 生成所有可能的条件
func generateAllConditions(data []RowData) []Condition {
	var conditions []Condition

	fmt.Println("正在生成所有可能的条件...")

	// 1. 生成单个元素条件（高价值）
	highValueSingles := []string{
		"a33", "a13", "a23", "a22", "a32", "a21", "a12", "a31", "a11",
		"b33", "b13", "b23", "b22", "b32", "b21", "b12", "b31", "b11",
		"c33", "c13", "c23", "c22", "c32", "c21", "c12", "c31", "c11",
		"d33", "d13", "d23", "d22", "d32", "d21", "d12", "d31", "d11",
	}

	for _, element := range highValueSingles {
		score := calculateScore([]string{element})
		if score >= 10 { // 只选择得分>=10的
			var col int
			switch element[0] {
			case 'a':
				col = 2 // C列
			case 'b':
				col = 7 // H列
			case 'c':
				col = 12 // M列
			case 'd':
				col = 17 // R列
			}

			conditions = append(conditions, Condition{
				Elements: []string{element},
				Columns:  []int{col},
				Score:    score,
				Type:     "single",
			})
		}
	}

	// 2. 生成一些精选的四元组合
	quadCombos := [][]string{
		{"az1", "bz1", "cz1", "dz1"}, // 低分值组合，高得分
		{"az2", "bz2", "cz2", "dz2"},
		{"az3", "bz3", "cz3", "dz3"},
		{"az1", "bz2", "cz3", "dz1"},
		{"az3", "bz1", "cz2", "dz3"},
		{"aq3", "bq3", "cq3", "dq3"}, // 中等分值组合
		{"aq2", "bq2", "cq2", "dq2"},
		{"aq1", "bq1", "cq1", "dq1"},
	}

	for _, combo := range quadCombos {
		score := calculateScore(combo)
		if score > 0 && score >= 1 {
			// 确定列索引
			cols := []int{0, 5, 10, 15} // A, F, K, P 列（特殊列的第一列）

			conditions = append(conditions, Condition{
				Elements: combo,
				Columns:  cols,
				Score:    score,
				Type:     "quad",
			})
		}
	}

	fmt.Printf("生成了 %d 个条件 (%d 单个, %d 四元组合)\n",
		len(conditions),
		len(conditions)-len(quadCombos),
		len(quadCombos))

	// 按得分排序
	sort.Slice(conditions, func(i, j int) bool {
		return conditions[i].Score > conditions[j].Score
	})

	return conditions
}

// 模拟筛选过程
func simulateFiltering(data []RowData, conditions []Condition) (bool, float64, int) {
	currentData := make([]RowData, len(data))
	copy(currentData, data)

	totalScore := 0.0

	for _, condition := range conditions {
		var newData []RowData
		matchCount := 0

		for _, row := range currentData {
			match := true
			for i, element := range condition.Elements {
				if row.Values[condition.Columns[i]] != element {
					match = false
					break
				}
			}

			if match {
				matchCount++
			} else {
				newData = append(newData, row)
			}
		}

		if matchCount == 0 {
			return false, totalScore, len(currentData)
		}

		currentData = newData
		totalScore += condition.Score
	}

	return len(currentData) == 0, totalScore, len(currentData)
}

// 随机搜索解决方案
func randomSearch(data []RowData, allConditions []Condition) {
	fmt.Println("=== 随机搜索解决方案 ===")

	targetMin := 100.0
	targetMax := 105.0

	rand.Seed(time.Now().UnixNano())

	maxIterations := 10000
	bestScore := 0.0
	var bestSolution []Condition

	for iteration := 0; iteration < maxIterations; iteration++ {
		if iteration%1000 == 0 {
			fmt.Printf("迭代 %d/%d, 当前最佳分数: %.2f\n", iteration, maxIterations, bestScore)
		}

		// 随机选择条件组合
		numConditions := rand.Intn(8) + 5 // 5-12个条件
		var selectedConditions []Condition
		usedIndices := make(map[int]bool)

		for len(selectedConditions) < numConditions {
			idx := rand.Intn(len(allConditions))
			if !usedIndices[idx] {
				selectedConditions = append(selectedConditions, allConditions[idx])
				usedIndices[idx] = true
			}
		}

		// 测试这个组合
		isComplete, totalScore, remaining := simulateFiltering(data, selectedConditions)

		if totalScore > bestScore {
			bestScore = totalScore
			bestSolution = selectedConditions
		}

		// 检查是否找到完美解决方案
		if isComplete && totalScore >= targetMin && totalScore <= targetMax {
			fmt.Printf("\n🎉 找到完美解决方案！\n")
			fmt.Printf("迭代次数: %d\n", iteration+1)
			fmt.Printf("总分: %.6f\n", totalScore)
			fmt.Printf("条件数量: %d\n", len(selectedConditions))

			fmt.Println("\n解决方案详情:")
			for i, condition := range selectedConditions {
				if condition.Type == "single" {
					fmt.Printf("%d. 单元素: %s (列%d) = %.6f\n",
						i+1, condition.Elements[0], condition.Columns[0], condition.Score)
				} else {
					fmt.Printf("%d. 四元组合: %v (列%v) = %.6f\n",
						i+1, condition.Elements, condition.Columns, condition.Score)
				}
			}
			return
		}

		// 显示接近的解决方案
		if remaining < 1000 && totalScore >= targetMin*0.9 {
			fmt.Printf("接近解决方案: 总分%.2f, 剩余%d行, %d个条件\n",
				totalScore, remaining, len(selectedConditions))
		}
	}

	fmt.Printf("\n搜索完成，最佳分数: %.6f\n", bestScore)
	if len(bestSolution) > 0 {
		fmt.Println("最佳解决方案:")
		for i, condition := range bestSolution {
			if condition.Type == "single" {
				fmt.Printf("%d. %s = %.6f\n", i+1, condition.Elements[0], condition.Score)
			} else {
				fmt.Printf("%d. %v = %.6f\n", i+1, condition.Elements, condition.Score)
			}
		}
	}
}

func main() {
	fmt.Println("=== 最终四元组合解决方案搜索程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 生成所有条件
	allConditions := generateAllConditions(data)

	// 随机搜索解决方案
	randomSearch(data, allConditions)
}
