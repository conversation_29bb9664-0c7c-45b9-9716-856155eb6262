package main

import (
	"fmt"
	"log"
	"sort"
	"strings"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type QuadCondition struct {
	Elements []string // 四个元素
	Columns  []int    // 对应的四个列
	Score    float64  // 得分
}

type Solution struct {
	Conditions []QuadCondition
	TotalScore float64
	Valid      bool
}

// 完整的元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "az4": 1.47, "az5": 4.05, "az6": 4.95,
	"bz1": 1.45, "bz2": 4.40, "bz3": 4.70, "bz4": 2.30, "bz5": 3.75, "bz6": 2.34,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "cz4": 1.50, "cz5": 3.90, "cz6": 4.85,
	"dz1": 1.92, "dz2": 3.18, "dz3": 3.40, "dz4": 1.67, "dz5": 3.50, "dz6": 4.10,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
	// 添加普通列元素
	"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
	"a31": 4.30, "a32": 6.25, "a33": 26.00,
	"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
	"b31": 8.15, "b32": 11.50, "b33": 34.00,
	"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
	"c31": 4.40, "c32": 6.50, "c33": 26.00,
	"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
	"d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算四元组合得分
func calculateQuadScore(elements []string) float64 {
	product := 1.0
	for _, element := range elements {
		if score, exists := ElementScores[element]; exists {
			product *= score
		} else {
			return 0
		}
	}
	return 100.0 / product
}

// 计算单个元素得分
func calculateSingleScore(element string) float64 {
	if score, exists := ElementScores[element]; exists {
		return 100.0 / score
	}
	return 0
}

// 生成混合条件（四元组合 + 单个元素）
func generateMixedConditions(data []RowData) ([]QuadCondition, []QuadCondition) {
	var quadConditions []QuadCondition
	var singleConditions []QuadCondition

	fmt.Println("正在生成混合条件...")

	// 1. 生成一些高价值的四元组合
	specialCols := []int{0, 1, 5, 6, 10, 11, 15, 16}
	groupValues := make(map[int][]string)

	for _, col := range specialCols {
		valueMap := make(map[string]bool)
		for _, row := range data {
			valueMap[row.Values[col]] = true
		}

		var values []string
		for value := range valueMap {
			if _, exists := ElementScores[value]; exists {
				values = append(values, value)
			}
		}
		groupValues[col] = values
	}

	// 生成四元组合（限制数量）
	count := 0
	maxQuads := 50

	for _, colA := range []int{0, 1} {
		for _, colB := range []int{5, 6} {
			for _, colC := range []int{10, 11} {
				for _, colD := range []int{15, 16} {
					if count >= maxQuads {
						break
					}

					valuesA := groupValues[colA]
					valuesB := groupValues[colB]
					valuesC := groupValues[colC]
					valuesD := groupValues[colD]

					// 只取每列的前2个值
					if len(valuesA) > 2 {
						valuesA = valuesA[:2]
					}
					if len(valuesB) > 2 {
						valuesB = valuesB[:2]
					}
					if len(valuesC) > 2 {
						valuesC = valuesC[:2]
					}
					if len(valuesD) > 2 {
						valuesD = valuesD[:2]
					}

					for _, vA := range valuesA {
						for _, vB := range valuesB {
							for _, vC := range valuesC {
								for _, vD := range valuesD {
									if count >= maxQuads {
										break
									}

									elements := []string{vA, vB, vC, vD}
									columns := []int{colA, colB, colC, colD}
									score := calculateQuadScore(elements)

									if score > 0 && score >= 1 && score <= 20 {
										quadConditions = append(quadConditions, QuadCondition{
											Elements: elements,
											Columns:  columns,
											Score:    score,
										})
										count++
									}
								}
								if count >= maxQuads {
									break
								}
							}
							if count >= maxQuads {
								break
							}
						}
						if count >= maxQuads {
							break
						}
					}
				}
				if count >= maxQuads {
					break
				}
			}
			if count >= maxQuads {
				break
			}
		}
		if count >= maxQuads {
			break
		}
	}

	// 2. 生成高价值的单个元素条件
	highValueElements := []string{
		"a11", "a12", "a21", "a31", "a32", "a33",
		"b11", "b12", "b21", "b31", "b32", "b33",
		"c11", "c12", "c21", "c31", "c32", "c33",
		"d11", "d12", "d21", "d31", "d32", "d33",
	}

	for _, element := range highValueElements {
		score := calculateSingleScore(element)
		if score >= 15 { // 只选择高分元素
			// 确定列索引
			var col int
			switch element[0] {
			case 'a':
				col = 2 // C列
			case 'b':
				col = 7 // H列
			case 'c':
				col = 12 // M列
			case 'd':
				col = 17 // R列
			}

			singleConditions = append(singleConditions, QuadCondition{
				Elements: []string{element},
				Columns:  []int{col},
				Score:    score,
			})
		}
	}

	fmt.Printf("生成了 %d 个四元组合, %d 个单个元素条件\n", len(quadConditions), len(singleConditions))

	return quadConditions, singleConditions
}

// 模拟完整筛选过程
func simulateCompleteFiltering(data []RowData, conditions []QuadCondition) (bool, float64, int) {
	currentData := make([]RowData, len(data))
	copy(currentData, data)

	totalScore := 0.0

	for _, condition := range conditions {
		// 应用筛选条件
		var newData []RowData
		matchCount := 0

		for _, row := range currentData {
			match := true
			for i, element := range condition.Elements {
				if row.Values[condition.Columns[i]] != element {
					match = false
					break
				}
			}

			if match {
				matchCount++
			} else {
				newData = append(newData, row)
			}
		}

		if matchCount == 0 {
			return false, totalScore, len(currentData)
		}

		currentData = newData
		totalScore += condition.Score
	}

	return len(currentData) == 0, totalScore, len(currentData)
}

// 寻找多四元组合解决方案
func findMultiQuadSolution(data []RowData) {
	fmt.Println("=== 寻找多四元组合解决方案 ===")

	quadConditions, singleConditions := generateMixedConditions(data)

	// 合并所有条件
	allConditions := append(quadConditions, singleConditions...)

	// 按得分排序
	sort.Slice(allConditions, func(i, j int) bool {
		return allConditions[i].Score > allConditions[j].Score
	})

	targetMin := 100.0
	targetMax := 105.0

	fmt.Printf("开始搜索解决方案（目标: %.1f - %.1f）...\n", targetMin, targetMax)

	// 尝试不同长度的组合
	for length := 8; length <= 15; length++ {
		fmt.Printf("\n尝试 %d 个条件的组合...\n", length)

		found := false
		tested := 0
		maxTests := 1000

		// 使用贪心算法构建组合
		for start := 0; start < len(allConditions) && start < 20; start++ {
			if tested >= maxTests {
				break
			}

			var combination []QuadCondition
			currentScore := 0.0
			usedConditions := make(map[int]bool)

			// 贪心选择条件
			for len(combination) < length && currentScore < targetMax*1.2 {
				bestIdx := -1
				bestScore := 0.0

				for i, condition := range allConditions {
					if usedConditions[i] {
						continue
					}

					newScore := currentScore + condition.Score
					if newScore <= targetMax*1.2 && condition.Score > bestScore {
						bestScore = condition.Score
						bestIdx = i
					}
				}

				if bestIdx == -1 {
					break
				}

				combination = append(combination, allConditions[bestIdx])
				currentScore += allConditions[bestIdx].Score
				usedConditions[bestIdx] = true
			}

			if len(combination) >= 5 { // 至少5个条件
				tested++

				isComplete, totalScore, remaining := simulateCompleteFiltering(data, combination)

				if isComplete && totalScore >= targetMin && totalScore <= targetMax {
					fmt.Printf("\n🎉 找到完美解决方案！\n")
					fmt.Printf("总分: %.6f\n", totalScore)
					fmt.Printf("条件数量: %d\n", len(combination))

					fmt.Println("\n解决方案详情:")
					for i, condition := range combination {
						if len(condition.Elements) == 1 {
							fmt.Printf("%d. 单元素: %s (列%d) = %.6f\n",
								i+1, condition.Elements[0], condition.Columns[0], condition.Score)
						} else {
							fmt.Printf("%d. 四元组合: %v (列%v) = %.6f\n",
								i+1, condition.Elements, condition.Columns, condition.Score)
						}
					}
					found = true
					return
				}

				// 显示接近的解决方案
				if remaining < 5000 && totalScore >= targetMin*0.8 && totalScore <= targetMax*1.3 {
					fmt.Printf("接近解决方案: 总分%.2f, 剩余%d行, %d个条件\n",
						totalScore, remaining, len(combination))
				}
			}
		}

		if found {
			break
		}
		fmt.Printf("测试了 %d 个组合\n", tested)
	}

	fmt.Println("搜索完成")
}

func main() {
	fmt.Println("=== 多四元组合解决方案搜索程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 寻找多四元组合解决方案
	findMultiQuadSolution(data)
}
