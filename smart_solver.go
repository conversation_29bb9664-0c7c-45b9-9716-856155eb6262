package main

import (
	"fmt"
	"log"
	"math"
	"sort"
	"strings"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type Solution struct {
	Elements   []string
	TotalScore float64
	Valid      bool
}

// 元素分值映射（简化版，只包含常用元素）
var ElementScores = map[string]float64{
	// 高频元素
	"a11": 3.60, "a12": 5.50, "a21": 6.00, "a31": 4.30, "a32": 6.25, "a33": 26.00,
	"b11": 2.05, "b12": 4.45, "b21": 8.75, "b31": 8.15, "b32": 11.50, "b33": 34.00,
	"c11": 3.60, "c12": 5.50, "c21": 5.60, "c31": 4.40, "c32": 6.50, "c33": 26.00,
	"d11": 3.05, "d12": 4.60, "d21": 5.10, "d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 列映射
var ColumnMapping = map[string]int{
	"a11": 2, "a12": 2, "a21": 2, "a31": 2, "a32": 2, "a33": 2, // C列
	"b11": 7, "b12": 7, "b21": 7, "b31": 7, "b32": 7, "b33": 7, // H列
	"c11": 12, "c12": 12, "c21": 12, "c31": 12, "c32": 12, "c33": 12, // M列
	"d11": 17, "d12": 17, "d21": 17, "d31": 17, "d32": 17, "d33": 17, // R列
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算元素得分
func calculateScore(element string) float64 {
	if baseScore, exists := ElementScores[element]; exists {
		return 100.0 / baseScore
	}
	return 0
}

// 模拟筛选过程
func simulateFiltering(data []RowData, elements []string) (bool, float64, int) {
	currentData := make([]RowData, len(data))
	copy(currentData, data)

	totalScore := 0.0

	for _, element := range elements {
		col, exists := ColumnMapping[element]
		if !exists {
			return false, 0, len(currentData)
		}

		// 找到匹配的行
		var newData []RowData
		matchCount := 0

		for _, row := range currentData {
			if row.Values[col] == element {
				matchCount++
			} else {
				newData = append(newData, row)
			}
		}

		if matchCount == 0 {
			return false, totalScore, len(currentData)
		}

		currentData = newData
		totalScore += calculateScore(element)
	}

	return len(currentData) == 0, totalScore, len(currentData)
}

// 寻找完整解决方案
func findCompleteSolution(data []RowData) {
	fmt.Println("=== 寻找完整解决方案 ===")

	// 获取所有可用元素
	var elements []string
	for element := range ElementScores {
		elements = append(elements, element)
	}

	// 按得分排序
	sort.Slice(elements, func(i, j int) bool {
		return calculateScore(elements[i]) > calculateScore(elements[j])
	})

	fmt.Printf("可用元素 %d 个\n", len(elements))

	targetMin := 100.0
	targetMax := 105.0

	var bestSolutions []Solution

	// 尝试不同长度的组合
	for length := 2; length <= 6; length++ {
		fmt.Printf("\n尝试 %d 元素组合...\n", length)

		solutions := generateCombinations(elements, length)
		fmt.Printf("生成 %d 个组合\n", len(solutions))

		validCount := 0
		for i, combination := range solutions {
			if i%1000 == 0 && i > 0 {
				fmt.Printf("已测试 %d 个组合...\n", i)
			}

			isComplete, totalScore, _ := simulateFiltering(data, combination)

			if isComplete && totalScore >= targetMin && totalScore <= targetMax {
				solution := Solution{
					Elements:   combination,
					TotalScore: totalScore,
					Valid:      true,
				}
				bestSolutions = append(bestSolutions, solution)
				validCount++

				fmt.Printf("✅ 找到解决方案 %d: %v = %.6f\n", validCount, combination, totalScore)

				// 找到一个就够了
				if validCount >= 1 {
					break
				}
			}
		}

		if len(bestSolutions) > 0 {
			break
		}
	}

	if len(bestSolutions) == 0 {
		fmt.Println("❌ 未找到完整解决方案")

		// 尝试找一些接近的解决方案
		fmt.Println("\n寻找接近的解决方案...")
		findNearSolutions(data, targetMin, targetMax)
	} else {
		fmt.Printf("\n🎉 找到 %d 个完整解决方案！\n", len(bestSolutions))
		for i, solution := range bestSolutions {
			fmt.Printf("%d. %v = %.6f\n", i+1, solution.Elements, solution.TotalScore)
		}
	}
}

// 生成组合
func generateCombinations(elements []string, length int) [][]string {
	var result [][]string

	var backtrack func(start int, current []string)
	backtrack = func(start int, current []string) {
		if len(current) == length {
			combination := make([]string, len(current))
			copy(combination, current)
			result = append(result, combination)
			return
		}

		for i := start; i < len(elements); i++ {
			current = append(current, elements[i])
			backtrack(i+1, current)
			current = current[:len(current)-1]
		}
	}

	backtrack(0, []string{})
	return result
}

// 寻找接近的解决方案
func findNearSolutions(data []RowData, targetMin, targetMax float64) {
	elements := []string{"a11", "a12", "a21", "a31", "b11", "b12", "b21", "b31", "c11", "c12", "c21", "c31", "d11", "d12", "d21", "d31"}

	type NearSolution struct {
		Elements   []string
		TotalScore float64
		Remaining  int
		Distance   float64
	}

	var nearSolutions []NearSolution
	center := (targetMin + targetMax) / 2

	// 尝试3元素组合
	combinations := generateCombinations(elements, 3)

	for _, combination := range combinations {
		_, totalScore, remaining := simulateFiltering(data, combination)

		if remaining < 1000 { // 剩余行数少于1000
			distance := math.Abs(totalScore - center)
			nearSolutions = append(nearSolutions, NearSolution{
				Elements:   combination,
				TotalScore: totalScore,
				Remaining:  remaining,
				Distance:   distance,
			})
		}
	}

	// 按剩余行数和距离排序
	sort.Slice(nearSolutions, func(i, j int) bool {
		if nearSolutions[i].Remaining != nearSolutions[j].Remaining {
			return nearSolutions[i].Remaining < nearSolutions[j].Remaining
		}
		return nearSolutions[i].Distance < nearSolutions[j].Distance
	})

	fmt.Printf("找到 %d 个接近的解决方案:\n", len(nearSolutions))
	for i := 0; i < 10 && i < len(nearSolutions); i++ {
		sol := nearSolutions[i]
		fmt.Printf("%d. %v = %.6f (剩余: %d行, 距离: %.2f)\n",
			i+1, sol.Elements, sol.TotalScore, sol.Remaining, sol.Distance)
	}
}

func main() {
	fmt.Println("=== 智能解决方案搜索程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 寻找完整解决方案
	findCompleteSolution(data)
}
