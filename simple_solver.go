package main

import (
	"fmt"
	"math"
	"sort"
)

// 元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "az4": 1.47, "az5": 4.05, "az6": 4.95,
	"bz1": 1.45, "bz2": 4.40, "bz3": 4.70, "bz4": 2.30, "bz5": 3.75, "bz6": 2.34,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "cz4": 1.50, "cz5": 3.90, "cz6": 4.85,
	"dz1": 1.92, "dz2": 3.18, "dz3": 3.40, "dz4": 1.67, "dz5": 3.50, "dz6": 4.10,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
	// A组合元素
	"a0w0": 13.00, "a0w1": 9.50, "a0w2": 13.00, "a0w3": 29.00, "a0w4": 90.00, "a0w5": 300.00,
	"a1w0": 9.00, "a1w1": 6.50, "a1w2": 8.00, "a1w3": 20.00, "a1w4": 60.00, "a1w5": 200.00,
	"a2w0": 12.00, "a2w1": 7.50, "a2w2": 11.00, "a2w3": 24.00, "a2w4": 75.00, "a2w5": 300.00,
	"a3w0": 23.00, "a3w1": 16.00, "a3w2": 22.00, "a3w3": 45.00,
	"a4w0": 60.00, "a4w1": 45.00, "a4w2": 70.00,
	"a5w0": 200.00, "a5w1": 150.00, "a5w2": 200.00,
	"a6w6": 75.00, "a7w7": 300.00, "a8w8": 80.00,
	"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
	"a31": 4.30, "a32": 6.25, "a33": 26.00,
	// B组合元素
	"b0w0": 27.00, "b0w1": 21.00, "b0w2": 33.00, "b0w3": 80.00, "b0w4": 250.00, "b0w5": 650.00,
	"b1w0": 12.00, "b1w1": 9.50, "b1w2": 14.00, "b1w3": 31.00, "b1w4": 100.00, "b1w5": 400.00,
	"b2w0": 9.50, "b2w1": 7.00, "b2w2": 11.50, "b2w3": 26.00, "b2w4": 90.00, "b2w5": 400.00,
	"b3w0": 13.00, "b3w1": 9.50, "b3w2": 14.00, "b3w3": 31.00,
	"b4w0": 22.00, "b4w1": 17.00, "b4w2": 25.00,
	"b5w0": 50.00, "b5w1": 35.00, "b5w2": 55.00,
	"b6w6": 15.00, "b7w7": 125.00, "b8w8": 75.00,
	"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
	"b31": 8.15, "b32": 11.50, "b33": 34.00,
	// C组合元素
	"c0w0": 12.00, "c0w1": 9.00, "c0w2": 13.00, "c0w3": 30.00, "c0w4": 90.00, "c0w5": 300.00,
	"c1w0": 8.00, "c1w1": 6.50, "c1w2": 8.50, "c1w3": 21.00, "c1w4": 75.00, "c1w5": 250.00,
	"c2w0": 10.50, "c2w1": 7.50, "c2w2": 12.00, "c2w3": 28.00, "c2w4": 90.00, "c2w5": 300.00,
	"c3w0": 21.00, "c3w1": 16.00, "c3w2": 24.00, "c3w3": 55.00,
	"c4w0": 60.00, "c4w1": 50.00, "c4w2": 75.00,
	"c5w0": 200.00, "c5w1": 150.00, "c5w2": 200.00,
	"c6w6": 75.00, "c7w7": 350.00, "c8w8": 100.00,
	"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
	"c31": 4.40, "c32": 6.50, "c33": 26.00,
	// D组合元素
	"d0w0": 10.50, "d0w1": 9.25, "d0w2": 16.00, "d0w3": 50.00, "d0w4": 150.00, "d0w5": 500.00,
	"d1w0": 7.00, "d1w1": 6.00, "d1w2": 9.50, "d1w3": 30.00, "d1w4": 100.00, "d1w5": 450.00,
	"d2w0": 8.75, "d2w1": 7.00, "d2w2": 12.00, "d2w3": 40.00, "d2w4": 150.00, "d2w5": 500.00,
	"d3w0": 16.00, "d3w1": 14.00, "d3w2": 26.00, "d3w3": 70.00,
	"d4w0": 50.00, "d4w1": 50.00, "d4w2": 75.00,
	"d5w0": 150.00, "d5w1": 150.00, "d5w2": 200.00,
	"d6w6": 75.00, "d7w7": 450.00, "d8w8": 150.00,
	"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
	"d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 简化测试：寻找分数在100-105之间的组合
func findSimpleSolution() {
	fmt.Println("=== 简化测试：寻找分数组合 ===")

	// 计算所有元素的得分
	type ElementScore struct {
		Element string
		Score   float64
	}

	var elements []ElementScore
	for element, baseScore := range ElementScores {
		score := 100.0 / baseScore
		elements = append(elements, ElementScore{
			Element: element,
			Score:   score,
		})
	}

	// 按得分排序
	sort.Slice(elements, func(i, j int) bool {
		return elements[i].Score > elements[j].Score
	})

	fmt.Printf("总共有 %d 个元素\n", len(elements))
	fmt.Println("前10个最高分元素:")
	for i := 0; i < 10 && i < len(elements); i++ {
		fmt.Printf("%d. %s: %.6f\n", i+1, elements[i].Element, elements[i].Score)
	}

	// 尝试找到分数在100-105之间的组合
	targetMin := 100.0
	targetMax := 105.0

	fmt.Printf("\n寻找分数在 %.2f - %.2f 之间的组合...\n", targetMin, targetMax)

	// 方法1：尝试单个元素
	fmt.Println("\n=== 单个元素方案 ===")
	for _, elem := range elements {
		if elem.Score >= targetMin && elem.Score <= targetMax {
			fmt.Printf("✅ 找到单个元素解决方案: %s = %.6f\n", elem.Element, elem.Score)
			return
		}
	}

	// 方法2：尝试两个元素组合
	fmt.Println("\n=== 两个元素组合方案 ===")
	found := false
	for i := 0; i < len(elements) && !found; i++ {
		for j := i + 1; j < len(elements) && !found; j++ {
			totalScore := elements[i].Score + elements[j].Score
			if totalScore >= targetMin && totalScore <= targetMax {
				fmt.Printf("✅ 找到两元素解决方案: %s (%.6f) + %s (%.6f) = %.6f\n",
					elements[i].Element, elements[i].Score,
					elements[j].Element, elements[j].Score,
					totalScore)
				found = true
			}
		}
	}

	if !found {
		// 方法3：尝试三个元素组合（限制搜索范围）
		fmt.Println("\n=== 三个元素组合方案（前50个元素）===")
		maxElements := 50
		if len(elements) < maxElements {
			maxElements = len(elements)
		}

		for i := 0; i < maxElements && !found; i++ {
			for j := i + 1; j < maxElements && !found; j++ {
				for k := j + 1; k < maxElements && !found; k++ {
					totalScore := elements[i].Score + elements[j].Score + elements[k].Score
					if totalScore >= targetMin && totalScore <= targetMax {
						fmt.Printf("✅ 找到三元素解决方案: %s (%.6f) + %s (%.6f) + %s (%.6f) = %.6f\n",
							elements[i].Element, elements[i].Score,
							elements[j].Element, elements[j].Score,
							elements[k].Element, elements[k].Score,
							totalScore)
						found = true
					}
				}
			}
		}
	}

	if !found {
		fmt.Println("❌ 在限定搜索范围内未找到解决方案")

		// 显示一些接近的组合
		fmt.Println("\n最接近的一些组合:")
		type Combination struct {
			Elements []string
			Scores   []float64
			Total    float64
			Distance float64
		}

		var closeCombinations []Combination
		center := (targetMin + targetMax) / 2

		// 收集接近的两元素组合
		for i := 0; i < 20 && i < len(elements); i++ {
			for j := i + 1; j < 20 && j < len(elements); j++ {
				totalScore := elements[i].Score + elements[j].Score
				distance := math.Abs(totalScore - center)
				if distance < 20 { // 只考虑距离中心20分以内的
					closeCombinations = append(closeCombinations, Combination{
						Elements: []string{elements[i].Element, elements[j].Element},
						Scores:   []float64{elements[i].Score, elements[j].Score},
						Total:    totalScore,
						Distance: distance,
					})
				}
			}
		}

		// 按距离排序
		sort.Slice(closeCombinations, func(i, j int) bool {
			return closeCombinations[i].Distance < closeCombinations[j].Distance
		})

		// 显示前5个最接近的
		for i := 0; i < 5 && i < len(closeCombinations); i++ {
			combo := closeCombinations[i]
			fmt.Printf("%d. %s + %s = %.6f (距离中心: %.6f)\n",
				i+1, combo.Elements[0], combo.Elements[1], combo.Total, combo.Distance)
		}
	}
}

func main() {
	findSimpleSolution()
}
