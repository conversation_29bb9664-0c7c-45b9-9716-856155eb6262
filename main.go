package main

import (
	"fmt"
	"log"
	"math"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type FilterCondition struct {
	Elements []string // 筛选元素，单个或组合
	Score    float64  // 得分
}

type WinningSequence struct {
	Conditions []FilterCondition
	TotalScore float64
}

// 列组定义
var (
	// 特殊列：必须组合筛选，不能单独使用
	SpecialCols = []int{0, 1, 5, 6, 10, 11, 15, 16} // A,B,F,G,K,L,P,Q
	// 普通列：可以单独筛选
	NormalCols = []int{2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19} // C,D,E,H,I,J,M,N,O,R,S,T

	// 列组映射
	ColGroups = map[int]int{
		0: 0, 1: 0, 2: 0, 3: 0, 4: 0, // 第一组 ABCDE
		5: 1, 6: 1, 7: 1, 8: 1, 9: 1, // 第二组 FGHIJ
		10: 2, 11: 2, 12: 2, 13: 2, 14: 2, // 第三组 KLMNO
		15: 3, 16: 3, 17: 3, 18: 3, 19: 3, // 第四组 PQRST
	}
)

// 元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "az4": 1.47, "az5": 4.05, "az6": 4.95,
	"bz1": 1.45, "bz2": 4.40, "bz3": 4.70, "bz4": 2.30, "bz5": 3.75, "bz6": 2.34,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "cz4": 1.50, "cz5": 3.90, "cz6": 4.85,
	"dz1": 1.92, "dz2": 3.18, "dz3": 3.40, "dz4": 1.67, "dz5": 3.50, "dz6": 4.10,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
	// A组合元素
	"a0w0": 13.00, "a0w1": 9.50, "a0w2": 13.00, "a0w3": 29.00, "a0w4": 90.00, "a0w5": 300.00,
	"a1w0": 9.00, "a1w1": 6.50, "a1w2": 8.00, "a1w3": 20.00, "a1w4": 60.00, "a1w5": 200.00,
	"a2w0": 12.00, "a2w1": 7.50, "a2w2": 11.00, "a2w3": 24.00, "a2w4": 75.00, "a2w5": 300.00,
	"a3w0": 23.00, "a3w1": 16.00, "a3w2": 22.00, "a3w3": 45.00,
	"a4w0": 60.00, "a4w1": 45.00, "a4w2": 70.00,
	"a5w0": 200.00, "a5w1": 150.00, "a5w2": 200.00,
	"a6w6": 75.00, "a7w7": 300.00, "a8w8": 80.00,
	"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
	"a31": 4.30, "a32": 6.25, "a33": 26.00,
	// B组合元素
	"b0w0": 27.00, "b0w1": 21.00, "b0w2": 33.00, "b0w3": 80.00, "b0w4": 250.00, "b0w5": 650.00,
	"b1w0": 12.00, "b1w1": 9.50, "b1w2": 14.00, "b1w3": 31.00, "b1w4": 100.00, "b1w5": 400.00,
	"b2w0": 9.50, "b2w1": 7.00, "b2w2": 11.50, "b2w3": 26.00, "b2w4": 90.00, "b2w5": 400.00,
	"b3w0": 13.00, "b3w1": 9.50, "b3w2": 14.00, "b3w3": 31.00,
	"b4w0": 22.00, "b4w1": 17.00, "b4w2": 25.00,
	"b5w0": 50.00, "b5w1": 35.00, "b5w2": 55.00,
	"b6w6": 15.00, "b7w7": 125.00, "b8w8": 75.00,
	"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
	"b31": 8.15, "b32": 11.50, "b33": 34.00,
	// C组合元素
	"c0w0": 12.00, "c0w1": 9.00, "c0w2": 13.00, "c0w3": 30.00, "c0w4": 90.00, "c0w5": 300.00,
	"c1w0": 8.00, "c1w1": 6.50, "c1w2": 8.50, "c1w3": 21.00, "c1w4": 75.00, "c1w5": 250.00,
	"c2w0": 10.50, "c2w1": 7.50, "c2w2": 12.00, "c2w3": 28.00, "c2w4": 90.00, "c2w5": 300.00,
	"c3w0": 21.00, "c3w1": 16.00, "c3w2": 24.00, "c3w3": 55.00,
	"c4w0": 60.00, "c4w1": 50.00, "c4w2": 75.00,
	"c5w0": 200.00, "c5w1": 150.00, "c5w2": 200.00,
	"c6w6": 75.00, "c7w7": 350.00, "c8w8": 100.00,
	"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
	"c31": 4.40, "c32": 6.50, "c33": 26.00,
	// D组合元素
	"d0w0": 10.50, "d0w1": 9.25, "d0w2": 16.00, "d0w3": 50.00, "d0w4": 150.00, "d0w5": 500.00,
	"d1w0": 7.00, "d1w1": 6.00, "d1w2": 9.50, "d1w3": 30.00, "d1w4": 100.00, "d1w5": 450.00,
	"d2w0": 8.75, "d2w1": 7.00, "d2w2": 12.00, "d2w3": 40.00, "d2w4": 150.00, "d2w5": 500.00,
	"d3w0": 16.00, "d3w1": 14.00, "d3w2": 26.00, "d3w3": 70.00,
	"d4w0": 50.00, "d4w1": 50.00, "d4w2": 75.00,
	"d5w0": 150.00, "d5w1": 150.00, "d5w2": 200.00,
	"d6w6": 75.00, "d7w7": 450.00, "d8w8": 150.00,
	"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
	"d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue // 跳过不完整的行
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算元素得分
func calculateScore(elements []string) float64 {
	if len(elements) == 1 {
		// 单个元素
		if score, exists := ElementScores[elements[0]]; exists {
			return 100.0 / score
		}
		return 0
	}

	// 组合元素
	product := 1.0
	for _, element := range elements {
		if score, exists := ElementScores[element]; exists {
			product *= score
		} else {
			return 0 // 如果任何元素不存在，返回0
		}
	}
	return 100.0 / product
}

// 检查行是否匹配筛选条件
func matchesFilter(row RowData, elements []string, cols []int) bool {
	if len(elements) != len(cols) {
		return false
	}

	for i, element := range elements {
		if row.Values[cols[i]] != element {
			return false
		}
	}
	return true
}

// 应用筛选条件，返回匹配的行索引
func applyFilter(data []RowData, elements []string, cols []int) []int {
	var matchedRows []int
	for i, row := range data {
		if matchesFilter(row, elements, cols) {
			matchedRows = append(matchedRows, i)
		}
	}
	return matchedRows
}

// 从数据中移除指定行
func removeRows(data []RowData, rowsToRemove []int) []RowData {
	if len(rowsToRemove) == 0 {
		return data
	}

	// 创建要移除的行的映射
	removeMap := make(map[int]bool)
	for _, idx := range rowsToRemove {
		removeMap[idx] = true
	}

	var result []RowData
	for i, row := range data {
		if !removeMap[i] {
			result = append(result, row)
		}
	}
	return result
}

// 获取列中的所有唯一值
func getUniqueValues(data []RowData, col int) []string {
	valueMap := make(map[string]bool)
	for _, row := range data {
		valueMap[row.Values[col]] = true
	}

	var values []string
	for value := range valueMap {
		values = append(values, value)
	}
	sort.Strings(values)
	return values
}

// 生成所有可能的组合筛选条件（优化版本）
func generateCombinations(data []RowData) []FilterCondition {
	var conditions []FilterCondition

	// 限制条件数量以提高性能
	maxConditionsPerType := 50

	// 1. 普通列的单独筛选
	for _, col := range NormalCols {
		values := getUniqueValues(data, col)
		count := 0
		for _, value := range values {
			if count >= maxConditionsPerType {
				break
			}
			score := calculateScore([]string{value})
			if score > 0 {
				conditions = append(conditions, FilterCondition{
					Elements: []string{value},
					Score:    score,
				})
				count++
			}
		}
	}

	// 2. 简化的特殊列组合筛选
	// 只生成一些高价值的组合
	conditions = append(conditions, generateSimpleSpecialCombinations(data)...)

	return conditions
}

// 生成简化的特殊列组合筛选条件
func generateSimpleSpecialCombinations(data []RowData) []FilterCondition {
	var conditions []FilterCondition

	// 预定义一些高价值的组合模式
	// 基于您提供的数据特征，生成一些典型组合

	// 获取各组特殊列的值
	groupAValues := []string{}
	groupBValues := []string{}
	groupCValues := []string{}
	groupDValues := []string{}

	// 收集A组特殊列的值（限制数量）
	for _, col := range []int{0, 1} { // A, B列
		values := getUniqueValues(data, col)
		if col == 0 { // A列
			for i, v := range values {
				if i < 3 { // 只取前3个
					groupAValues = append(groupAValues, v)
				}
			}
		}
	}

	// 收集其他组的值（类似处理）
	for _, col := range []int{5, 6} { // F, G列
		values := getUniqueValues(data, col)
		if col == 5 { // F列
			for i, v := range values {
				if i < 3 {
					groupBValues = append(groupBValues, v)
				}
			}
		}
	}

	for _, col := range []int{10, 11} { // K, L列
		values := getUniqueValues(data, col)
		if col == 10 { // K列
			for i, v := range values {
				if i < 3 {
					groupCValues = append(groupCValues, v)
				}
			}
		}
	}

	for _, col := range []int{15, 16} { // P, Q列
		values := getUniqueValues(data, col)
		if col == 15 { // P列
			for i, v := range values {
				if i < 3 {
					groupDValues = append(groupDValues, v)
				}
			}
		}
	}

	// 生成2元素组合
	maxCombinations := 20
	count := 0

	for _, aVal := range groupAValues {
		for _, bVal := range groupBValues {
			if count >= maxCombinations {
				break
			}
			elements := []string{aVal, bVal}
			score := calculateScore(elements)
			if score > 0 {
				conditions = append(conditions, FilterCondition{
					Elements: elements,
					Score:    score,
				})
				count++
			}
		}
		if count >= maxCombinations {
			break
		}
	}

	return conditions
}

// 生成特殊列的组合筛选条件
func generateSpecialCombinations(data []RowData, size int) []FilterCondition {
	var conditions []FilterCondition

	// 获取每组的特殊列
	groups := [][]int{
		{0, 1},   // 第一组的特殊列 A,B
		{5, 6},   // 第二组的特殊列 F,G
		{10, 11}, // 第三组的特殊列 K,L
		{15, 16}, // 第四组的特殊列 P,Q
	}

	// 生成组合
	combinations := generateGroupCombinations(groups, size)

	for _, colCombination := range combinations {
		// 为每个列组合生成所有可能的值组合
		valueCombinations := generateValueCombinations(data, colCombination)

		for _, valueCombination := range valueCombinations {
			score := calculateScore(valueCombination)
			if score > 0 {
				conditions = append(conditions, FilterCondition{
					Elements: valueCombination,
					Score:    score,
				})
			}
		}
	}

	return conditions
}

// 生成组的组合（确保来自不同组）
func generateGroupCombinations(groups [][]int, size int) [][]int {
	var result [][]int

	// 生成组索引的组合
	groupIndices := make([]int, len(groups))
	for i := range groupIndices {
		groupIndices[i] = i
	}

	groupCombinations := combinations(groupIndices, size)

	for _, groupCombo := range groupCombinations {
		// 为每个组合生成列的组合
		colCombinations := generateColCombinations(groups, groupCombo)
		result = append(result, colCombinations...)
	}

	return result
}

// 生成列的组合
func generateColCombinations(groups [][]int, groupIndices []int) [][]int {
	if len(groupIndices) == 0 {
		return [][]int{{}}
	}

	firstGroup := groups[groupIndices[0]]
	restCombinations := generateColCombinations(groups, groupIndices[1:])

	var result [][]int
	for _, col := range firstGroup {
		for _, restCombo := range restCombinations {
			newCombo := append([]int{col}, restCombo...)
			result = append(result, newCombo)
		}
	}

	return result
}

// 生成数组的组合
func combinations(arr []int, r int) [][]int {
	var result [][]int

	var backtrack func(start int, current []int)
	backtrack = func(start int, current []int) {
		if len(current) == r {
			combo := make([]int, len(current))
			copy(combo, current)
			result = append(result, combo)
			return
		}

		for i := start; i < len(arr); i++ {
			current = append(current, arr[i])
			backtrack(i+1, current)
			current = current[:len(current)-1]
		}
	}

	backtrack(0, []int{})
	return result
}

// 生成值的组合
func generateValueCombinations(data []RowData, cols []int) [][]string {
	if len(cols) == 0 {
		return [][]string{{}}
	}

	firstCol := cols[0]
	firstValues := getUniqueValues(data, firstCol)
	restCombinations := generateValueCombinations(data, cols[1:])

	var result [][]string
	for _, value := range firstValues {
		for _, restCombo := range restCombinations {
			newCombo := append([]string{value}, restCombo...)
			result = append(result, newCombo)
		}
	}

	return result
}

// 获取筛选条件对应的列索引
func getColumnsForCondition(condition FilterCondition) []int {
	if len(condition.Elements) == 1 {
		// 单个元素，需要找到对应的列
		element := condition.Elements[0]
		for _, col := range NormalCols {
			// 这里需要根据元素的前缀判断属于哪一列
			// 简化处理，通过元素名称推断列
			if colIndex := inferColumnFromElement(element); colIndex == col {
				return []int{col}
			}
		}
	} else {
		// 组合元素，需要根据元素推断列组合
		return inferColumnsFromElements(condition.Elements)
	}
	return []int{}
}

// 从元素推断列索引
func inferColumnFromElement(element string) int {
	// 根据元素的命名规则推断列
	if strings.HasPrefix(element, "a") && len(element) >= 2 {
		switch element[1] {
		case '1', '2', '3':
			return 2 // C列
		case 'q':
			return 3 // D列
		default:
			return 4 // E列
		}
	} else if strings.HasPrefix(element, "b") && len(element) >= 2 {
		switch element[1] {
		case '1', '2', '3':
			return 7 // H列
		case 'q':
			return 8 // I列
		default:
			return 9 // J列
		}
	} else if strings.HasPrefix(element, "c") && len(element) >= 2 {
		switch element[1] {
		case '1', '2', '3':
			return 12 // M列
		case 'q':
			return 13 // N列
		default:
			return 14 // O列
		}
	} else if strings.HasPrefix(element, "d") && len(element) >= 2 {
		switch element[1] {
		case '1', '2', '3':
			return 17 // R列
		case 'q':
			return 18 // S列
		default:
			return 19 // T列
		}
	}
	return -1
}

// 从多个元素推断列组合
func inferColumnsFromElements(elements []string) []int {
	var cols []int
	for _, element := range elements {
		col := inferColumnFromElement(element)
		if col >= 0 {
			cols = append(cols, col)
		}
	}
	return cols
}

// 寻找最优中奖序列
func findOptimalSequence(data []RowData, targetMin, targetMax float64) *WinningSequence {
	fmt.Printf("开始寻找最优中奖序列，目标分数范围: %.2f - %.2f\n", targetMin, targetMax)
	fmt.Printf("初始数据行数: %d\n", len(data))

	// 使用并发处理
	numWorkers := runtime.NumCPU()
	runtime.GOMAXPROCS(numWorkers)

	resultChan := make(chan *WinningSequence, numWorkers)
	var wg sync.WaitGroup

	// 启动多个工作协程
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			result := searchSequence(data, targetMin, targetMax, workerID)
			if result != nil {
				resultChan <- result
			}
		}(i)
	}

	// 等待所有工作协程完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var bestSequence *WinningSequence
	for sequence := range resultChan {
		if bestSequence == nil ||
			(sequence.TotalScore >= targetMin && sequence.TotalScore <= targetMax &&
				(bestSequence.TotalScore < targetMin || bestSequence.TotalScore > targetMax ||
					math.Abs(sequence.TotalScore-102.5) < math.Abs(bestSequence.TotalScore-102.5))) {
			bestSequence = sequence
		}
	}

	return bestSequence
}

// 搜索中奖序列（单个工作协程）- 简化版本
func searchSequence(originalData []RowData, targetMin, targetMax float64, workerID int) *WinningSequence {
	fmt.Printf("工作协程 %d 开始搜索...\n", workerID)

	// 使用简单的贪心算法
	maxIterations := 100 // 减少迭代次数

	for iteration := 0; iteration < maxIterations; iteration++ {
		data := make([]RowData, len(originalData))
		copy(data, originalData)

		var sequence WinningSequence
		currentScore := 0.0

		// 智能贪心搜索
		for len(data) > 0 && currentScore < targetMax*1.2 { // 允许少量超出
			// 使用智能条件选择
			conditions := getSmartConditions(data, currentScore, targetMin, targetMax)
			if len(conditions) == 0 {
				break
			}

			// 选择最佳条件
			bestCondition := conditions[0]
			cols := getColumnsForCondition(bestCondition)
			if len(cols) == 0 {
				continue
			}

			matchedRows := applyFilter(data, bestCondition.Elements, cols)
			if len(matchedRows) == 0 {
				continue
			}

			// 应用筛选
			data = removeRows(data, matchedRows)
			sequence.Conditions = append(sequence.Conditions, bestCondition)
			currentScore += bestCondition.Score

			fmt.Printf("工作协程 %d: 应用条件 %v，得分 %.2f，总分 %.2f，剩余行数 %d\n",
				workerID, bestCondition.Elements, bestCondition.Score, currentScore, len(data))

			// 如果达到目标范围且所有数据被删除
			if len(data) == 0 && currentScore >= targetMin && currentScore <= targetMax {
				sequence.TotalScore = currentScore
				fmt.Printf("工作协程 %d 找到解决方案！总分: %.6f\n", workerID, currentScore)
				return &sequence
			}
		}

		// 如果所有数据都被删除且分数在范围内
		if len(data) == 0 && currentScore >= targetMin && currentScore <= targetMax {
			sequence.TotalScore = currentScore
			fmt.Printf("工作协程 %d 找到解决方案！总分: %.6f\n", workerID, currentScore)
			return &sequence
		}
	}

	fmt.Printf("工作协程 %d 未找到解决方案\n", workerID)
	return nil
}

// 获取智能筛选条件
func getSmartConditions(data []RowData, currentScore, targetMin, targetMax float64) []FilterCondition {
	var conditions []FilterCondition

	// 计算还需要的分数范围
	remainingMin := targetMin - currentScore
	remainingMax := targetMax - currentScore

	// 如果已经接近目标，寻找精确匹配的条件
	if remainingMin <= 50 && remainingMax >= 10 {
		// 寻找分数在合适范围内的单个元素
		for element, baseScore := range ElementScores {
			score := 100.0 / baseScore
			if score >= remainingMin*0.8 && score <= remainingMax*1.2 {
				col := inferColumnFromElement(element)
				if col >= 0 && hasElementInData(data, col, element) {
					conditions = append(conditions, FilterCondition{
						Elements: []string{element},
						Score:    score,
					})
				}
			}
		}
	}

	// 如果没有找到合适的单个元素，尝试一些常见的高价值元素
	if len(conditions) == 0 {
		commonElements := []string{
			"a11", "a12", "a21", "a31", "a32", "a33",
			"b11", "b12", "b21", "b31", "b32", "b33",
			"c11", "c12", "c21", "c31", "c32", "c33",
			"d11", "d12", "d21", "d31", "d32", "d33",
		}

		for _, element := range commonElements {
			score := calculateScore([]string{element})
			if score > 0 {
				col := inferColumnFromElement(element)
				if col >= 0 && hasElementInData(data, col, element) {
					conditions = append(conditions, FilterCondition{
						Elements: []string{element},
						Score:    score,
					})
				}
			}
		}
	}

	// 按得分排序，优先选择能让总分接近目标的
	sort.Slice(conditions, func(i, j int) bool {
		scoreI := currentScore + conditions[i].Score
		scoreJ := currentScore + conditions[j].Score

		// 优先选择能进入目标范围的
		inRangeI := scoreI >= targetMin && scoreI <= targetMax
		inRangeJ := scoreJ >= targetMin && scoreJ <= targetMax

		if inRangeI && !inRangeJ {
			return true
		}
		if !inRangeI && inRangeJ {
			return false
		}

		// 都在范围内或都不在范围内，选择更接近目标中心的
		center := (targetMin + targetMax) / 2
		distI := math.Abs(scoreI - center)
		distJ := math.Abs(scoreJ - center)
		return distI < distJ
	})

	// 限制返回数量
	if len(conditions) > 5 {
		conditions = conditions[:5]
	}

	return conditions
}

// 检查元素是否在数据中存在
func hasElementInData(data []RowData, col int, element string) bool {
	for _, row := range data {
		if row.Values[col] == element {
			return true
		}
	}
	return false
}

// 验证中奖序列
func validateSequence(originalData []RowData, sequence *WinningSequence) bool {
	data := make([]RowData, len(originalData))
	copy(data, originalData)

	totalScore := 0.0

	for _, condition := range sequence.Conditions {
		cols := getColumnsForCondition(condition)
		if len(cols) == 0 {
			fmt.Printf("❌ 无法确定条件 %v 对应的列\n", condition.Elements)
			return false
		}

		matchedRows := applyFilter(data, condition.Elements, cols)
		if len(matchedRows) == 0 {
			fmt.Printf("❌ 条件 %v 没有匹配的行\n", condition.Elements)
			return false
		}

		data = removeRows(data, matchedRows)
		totalScore += condition.Score

		fmt.Printf("✓ 应用条件 %v，删除 %d 行，剩余 %d 行，当前得分: %.6f\n",
			condition.Elements, len(matchedRows), len(data), totalScore)
	}

	if len(data) != 0 {
		fmt.Printf("❌ 序列执行完毕后还剩余 %d 行数据\n", len(data))
		return false
	}

	if math.Abs(totalScore-sequence.TotalScore) > 0.000001 {
		fmt.Printf("❌ 计算得分不匹配: 期望 %.6f，实际 %.6f\n", sequence.TotalScore, totalScore)
		return false
	}

	return true
}

func main() {
	fmt.Println("=== 大数据处理专家 - Excel数据自动筛选程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 显示前几行数据作为示例
	fmt.Println("\n前5行数据示例:")
	for i := 0; i < 5 && i < len(data); i++ {
		fmt.Printf("第%d行: %v\n", i+1, data[i].Values)
	}

	// 设置目标分数范围
	targetMin := 100.0
	targetMax := 105.0

	fmt.Printf("\n开始寻找中奖序列，目标分数范围: %.2f - %.2f\n", targetMin, targetMax)

	startTime := time.Now()

	// 寻找最优中奖序列
	sequence := findOptimalSequence(data, targetMin, targetMax)

	elapsed := time.Since(startTime)

	if sequence != nil {
		fmt.Printf("\n🎉 找到最优中奖序列！\n")
		fmt.Printf("总分: %.6f\n", sequence.TotalScore)
		fmt.Printf("序列长度: %d\n", len(sequence.Conditions))
		fmt.Printf("计算耗时: %v\n", elapsed)

		fmt.Println("\n中奖序列详情:")
		for i, condition := range sequence.Conditions {
			fmt.Printf("%d. 元素: %v, 得分: %.6f\n", i+1, condition.Elements, condition.Score)
		}

		// 验证序列
		fmt.Println("\n正在验证序列...")
		if validateSequence(data, sequence) {
			fmt.Println("✅ 序列验证通过！")
		} else {
			fmt.Println("❌ 序列验证失败！")
		}

	} else {
		fmt.Printf("\n❌ 未找到满足条件的中奖序列\n")
		fmt.Printf("计算耗时: %v\n", elapsed)

		// 尝试放宽条件
		fmt.Println("\n尝试放宽分数范围...")
		relaxedMin := 95.0
		relaxedMax := 110.0

		relaxedSequence := findOptimalSequence(data, relaxedMin, relaxedMax)
		if relaxedSequence != nil {
			fmt.Printf("在放宽的范围(%.2f - %.2f)内找到序列，总分: %.6f\n",
				relaxedMin, relaxedMax, relaxedSequence.TotalScore)
		}
	}
}
