package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 分析每列的唯一值分布
func analyzeColumns(data []RowData) {
	fmt.Println("=== 列数据分析 ===")

	// 分析每列的唯一值
	for col := 0; col < 20; col++ {
		valueCount := make(map[string]int)
		for _, row := range data {
			valueCount[row.Values[col]]++
		}

		fmt.Printf("列 %d (%c): %d 个唯一值\n", col, 'A'+col, len(valueCount))

		// 显示每个值的分布
		for value, count := range valueCount {
			fmt.Printf("  %s: %d 行\n", value, count)
		}
		fmt.Println()
	}
}

// 寻找能覆盖所有数据的最小元素集合
func findMinimalCoverage(data []RowData) {
	fmt.Println("=== 寻找最小覆盖集合 ===")

	// 重要列的索引（根据您的描述，这些是可以单独筛选的列）
	importantCols := []int{2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19} // C,D,E,H,I,J,M,N,O,R,S,T

	// 为每列找到最频繁的值
	type ColValue struct {
		Col   int
		Value string
		Count int
		Score float64
	}

	var candidates []ColValue

	// 元素得分映射
	elementScores := map[string]float64{
		"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
		"a31": 4.30, "a32": 6.25, "a33": 26.00,
		"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
		"b31": 8.15, "b32": 11.50, "b33": 34.00,
		"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
		"c31": 4.40, "c32": 6.50, "c33": 26.00,
		"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
		"d31": 5.80, "d32": 7.50, "d33": 38.00,
	}

	for _, col := range importantCols {
		valueCount := make(map[string]int)
		for _, row := range data {
			valueCount[row.Values[col]]++
		}

		// 找到这一列中最频繁的值
		for value, count := range valueCount {
			if baseScore, exists := elementScores[value]; exists {
				score := 100.0 / baseScore
				candidates = append(candidates, ColValue{
					Col:   col,
					Value: value,
					Count: count,
					Score: score,
				})
			}
		}
	}

	fmt.Printf("找到 %d 个候选元素\n", len(candidates))

	// 使用贪心算法找到最小覆盖
	currentData := make([]RowData, len(data))
	copy(currentData, data)

	var solution []string
	totalScore := 0.0

	for len(currentData) > 0 {
		// 找到能删除最多行的元素
		bestCandidate := ColValue{}
		bestRemoveCount := 0

		for _, candidate := range candidates {
			removeCount := 0
			for _, row := range currentData {
				if row.Values[candidate.Col] == candidate.Value {
					removeCount++
				}
			}

			if removeCount > bestRemoveCount {
				bestRemoveCount = removeCount
				bestCandidate = candidate
			}
		}

		if bestRemoveCount == 0 {
			fmt.Printf("❌ 无法继续删除数据，剩余 %d 行\n", len(currentData))
			break
		}

		// 应用最佳候选
		var newData []RowData
		for _, row := range currentData {
			if row.Values[bestCandidate.Col] != bestCandidate.Value {
				newData = append(newData, row)
			}
		}

		currentData = newData
		solution = append(solution, bestCandidate.Value)
		totalScore += bestCandidate.Score

		fmt.Printf("选择 %s (列%d): 删除 %d 行, 剩余 %d 行, 得分 %.2f, 总分 %.2f\n",
			bestCandidate.Value, bestCandidate.Col, bestRemoveCount, len(currentData),
			bestCandidate.Score, totalScore)

		// 如果总分已经超过105，停止
		if totalScore > 105 {
			fmt.Printf("⚠️  总分超过105，停止搜索\n")
			break
		}
	}

	fmt.Printf("\n=== 最终结果 ===\n")
	fmt.Printf("解决方案: %v\n", solution)
	fmt.Printf("总分: %.6f\n", totalScore)
	fmt.Printf("剩余行数: %d\n", len(currentData))

	if len(currentData) == 0 {
		if totalScore >= 100.0 && totalScore <= 105.0 {
			fmt.Printf("🎉 找到完美解决方案！\n")
		} else {
			fmt.Printf("⚠️  所有数据被删除，但得分不在目标范围内 (100-105)\n")
		}
	} else {
		fmt.Printf("❌ 解决方案不完整\n")

		// 分析剩余数据
		fmt.Println("\n剩余数据分析:")
		for col := 0; col < 20; col++ {
			valueCount := make(map[string]int)
			for _, row := range currentData {
				valueCount[row.Values[col]]++
			}

			if len(valueCount) > 0 {
				fmt.Printf("列 %d: ", col)
				for value, count := range valueCount {
					fmt.Printf("%s(%d) ", value, count)
				}
				fmt.Println()
			}
		}
	}
}

// 尝试特定的解决方案组合
func trySpecificSolutions(data []RowData) {
	fmt.Println("\n=== 尝试特定解决方案 ===")

	// 基于理论分析的一些可能的解决方案
	solutions := [][]string{
		{"a11", "b11", "c11", "d11"}, // 四个最常见的11元素
		{"a31", "b31", "c31", "d31"}, // 四个31元素
		{"a12", "b12", "c12", "d12"}, // 四个12元素
		{"a11", "b12", "c31", "d21"}, // 混合组合
		{"a21", "b21", "c21", "d21"}, // 四个21元素
	}

	for i, solution := range solutions {
		fmt.Printf("\n测试解决方案 %d: %v\n", i+1, solution)

		currentData := make([]RowData, len(data))
		copy(currentData, data)

		totalScore := 0.0
		valid := true

		for _, element := range solution {
			// 确定列索引
			var col int
			switch element[0] {
			case 'a':
				col = 2 // C列
			case 'b':
				col = 7 // H列
			case 'c':
				col = 12 // M列
			case 'd':
				col = 17 // R列
			default:
				valid = false
				break
			}

			if !valid {
				break
			}

			// 计算得分
			var score float64
			switch element {
			case "a11":
				score = 100.0 / 3.60
			case "a12":
				score = 100.0 / 5.50
			case "a21":
				score = 100.0 / 6.00
			case "a31":
				score = 100.0 / 4.30
			case "b11":
				score = 100.0 / 2.05
			case "b12":
				score = 100.0 / 4.45
			case "b21":
				score = 100.0 / 8.75
			case "b31":
				score = 100.0 / 8.15
			case "c11":
				score = 100.0 / 3.60
			case "c12":
				score = 100.0 / 5.50
			case "c21":
				score = 100.0 / 5.60
			case "c31":
				score = 100.0 / 4.40
			case "d11":
				score = 100.0 / 3.05
			case "d12":
				score = 100.0 / 4.60
			case "d21":
				score = 100.0 / 5.10
			case "d31":
				score = 100.0 / 5.80
			default:
				score = 0
			}

			// 筛选数据
			var newData []RowData
			removeCount := 0
			for _, row := range currentData {
				if row.Values[col] == element {
					removeCount++
				} else {
					newData = append(newData, row)
				}
			}

			currentData = newData
			totalScore += score

			fmt.Printf("  %s: 删除 %d 行, 剩余 %d 行, 得分 %.2f\n",
				element, removeCount, len(currentData), score)
		}

		fmt.Printf("总分: %.6f, 剩余行数: %d\n", totalScore, len(currentData))

		if len(currentData) == 0 && totalScore >= 100.0 && totalScore <= 105.0 {
			fmt.Printf("🎉 找到完美解决方案！\n")
			return
		}
	}
}

func main() {
	fmt.Println("=== 最终解决方案程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 分析数据结构
	// analyzeColumns(data) // 取消注释以查看详细分析

	// 寻找最小覆盖
	findMinimalCoverage(data)

	// 尝试特定解决方案
	trySpecificSolutions(data)
}
