package main

import (
	"fmt"
	"log"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type FilterCondition struct {
	Elements []string // 筛选元素，单个或组合
	Score    float64  // 得分
	Type     string   // "single", "quad"
}

type WinningSequence struct {
	Conditions []FilterCondition
	TotalScore float64
}

// 完整的元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "bz1": 1.45, "bz2": 4.40, "bz3": 4.70,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "dz1": 1.92, "dz2": 3.18, "dz3": 3.40,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
	"a11": 3.60, "a12": 5.50, "a13": 24.00, "a21": 6.00, "a22": 13.00, "a23": 13.00,
	"a31": 4.30, "a32": 6.25, "a33": 26.00,
	"b11": 2.05, "b12": 4.45, "b13": 18.00, "b21": 8.75, "b22": 15.00, "b23": 15.00,
	"b31": 8.15, "b32": 11.50, "b33": 34.00,
	"c11": 3.60, "c12": 5.50, "c13": 24.00, "c21": 5.60, "c22": 13.00, "c23": 13.00,
	"c31": 4.40, "c32": 6.50, "c33": 26.00,
	"d11": 3.05, "d12": 4.60, "d13": 26.00, "d21": 5.10, "d22": 14.00, "d23": 14.00,
	"d31": 5.80, "d32": 7.50, "d33": 38.00,
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算得分
func calculateScore(elements []string) float64 {
	if len(elements) == 1 {
		if score, exists := ElementScores[elements[0]]; exists {
			return 100.0 / score
		}
	} else {
		product := 1.0
		for _, element := range elements {
			if score, exists := ElementScores[element]; exists {
				product *= score
			} else {
				return 0
			}
		}
		return 100.0 / product
	}
	return 0
}

// 生成重点关注四元组合的条件
func generateQuadFocusedConditions(data []RowData) []FilterCondition {
	var conditions []FilterCondition

	fmt.Println("正在生成重点关注四元组合的条件...")

	// 1. 生成精选的四元组合
	quadCombos := [][]string{
		{"az1", "bz1", "cz1", "dz1"}, // 高得分组合
		{"az2", "bz2", "cz2", "dz2"},
		{"az3", "bz3", "cz3", "dz3"},
		{"az1", "bz2", "cz3", "dz1"}, // 混合组合
		{"az3", "bz1", "cz2", "dz3"},
		{"aq3", "bq3", "cq3", "dq3"}, // 中等分值组合
		{"aq2", "bq2", "cq2", "dq2"},
		{"aq1", "bq1", "cq1", "dq1"},
	}

	for _, combo := range quadCombos {
		score := calculateScore(combo)
		if score > 0 {
			conditions = append(conditions, FilterCondition{
				Elements: combo,
				Score:    score,
				Type:     "quad",
			})
		}
	}

	// 2. 添加一些高价值的单个元素作为补充
	highValueSingles := []string{
		"a33", "b33", "c33", "d33", // 最高分
		"a13", "b13", "c13", "d13", // 次高分
		"a23", "b23", "c23", "d23", // 中高分
	}

	for _, element := range highValueSingles {
		score := calculateScore([]string{element})
		if score >= 15 { // 只选择高分元素
			conditions = append(conditions, FilterCondition{
				Elements: []string{element},
				Score:    score,
				Type:     "single",
			})
		}
	}

	fmt.Printf("生成了 %d 个条件\n", len(conditions))

	// 按得分排序
	sort.Slice(conditions, func(i, j int) bool {
		// 优先四元组合，然后按得分排序
		if conditions[i].Type == "quad" && conditions[j].Type != "quad" {
			return true
		}
		if conditions[i].Type != "quad" && conditions[j].Type == "quad" {
			return false
		}
		return conditions[i].Score > conditions[j].Score
	})

	return conditions
}

// 获取筛选条件对应的列索引
func getColumnsForCondition(condition FilterCondition) []int {
	if condition.Type == "single" {
		element := condition.Elements[0]
		var col int
		switch element[0] {
		case 'a':
			col = 2 // C列
		case 'b':
			col = 7 // H列
		case 'c':
			col = 12 // M列
		case 'd':
			col = 17 // R列
		default:
			return []int{}
		}
		return []int{col}
	} else if condition.Type == "quad" {
		// 四元组合使用特殊列：A(0), F(5), K(10), P(15)
		return []int{0, 5, 10, 15}
	}
	return []int{}
}

// 应用筛选条件
func applyFilter(data []RowData, elements []string, cols []int) []int {
	var matchedRows []int
	for i, row := range data {
		match := true
		for j, element := range elements {
			if j < len(cols) && row.Values[cols[j]] != element {
				match = false
				break
			}
		}
		if match {
			matchedRows = append(matchedRows, i)
		}
	}
	return matchedRows
}

// 从数据中移除指定行
func removeRows(data []RowData, rowsToRemove []int) []RowData {
	if len(rowsToRemove) == 0 {
		return data
	}

	removeMap := make(map[int]bool)
	for _, idx := range rowsToRemove {
		removeMap[idx] = true
	}

	var result []RowData
	for i, row := range data {
		if !removeMap[i] {
			result = append(result, row)
		}
	}
	return result
}

// 智能搜索解决方案
func smartSearch(data []RowData, targetMin, targetMax float64) *WinningSequence {
	fmt.Printf("开始智能搜索，目标分数: %.1f - %.1f\n", targetMin, targetMax)

	conditions := generateQuadFocusedConditions(data)

	// 使用并发搜索
	numWorkers := runtime.NumCPU()
	resultChan := make(chan *WinningSequence, numWorkers)
	var wg sync.WaitGroup

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			// 每个工作协程尝试不同的组合策略
			maxAttempts := 500
			for attempt := 0; attempt < maxAttempts; attempt++ {
				sequence := tryRandomCombination(data, conditions, targetMin, targetMax, workerID, attempt)
				if sequence != nil {
					resultChan <- sequence
					return
				}
			}
		}(i)
	}

	// 等待结果
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集最佳结果
	var bestSequence *WinningSequence
	for sequence := range resultChan {
		if bestSequence == nil ||
			(sequence.TotalScore >= targetMin && sequence.TotalScore <= targetMax) {
			bestSequence = sequence
			break // 找到第一个满足条件的就返回
		}
	}

	return bestSequence
}

// 尝试随机组合
func tryRandomCombination(data []RowData, conditions []FilterCondition, targetMin, targetMax float64, workerID, attempt int) *WinningSequence {
	// 构建一个随机的条件组合
	var selectedConditions []FilterCondition
	currentScore := 0.0

	// 优先选择四元组合
	quadCount := 0
	for _, condition := range conditions {
		if condition.Type == "quad" && quadCount < 3 { // 最多3个四元组合
			if currentScore+condition.Score <= targetMax*1.1 {
				selectedConditions = append(selectedConditions, condition)
				currentScore += condition.Score
				quadCount++
			}
		}
	}

	// 然后添加单个元素
	for _, condition := range conditions {
		if condition.Type == "single" && len(selectedConditions) < 10 {
			if currentScore+condition.Score <= targetMax*1.1 {
				selectedConditions = append(selectedConditions, condition)
				currentScore += condition.Score
			}
		}
	}

	// 检查分数是否在目标范围内
	if currentScore < targetMin || currentScore > targetMax {
		return nil
	}

	// 验证这个组合是否能删除所有数据
	if validateCombination(data, selectedConditions) {
		return &WinningSequence{
			Conditions: selectedConditions,
			TotalScore: currentScore,
		}
	}

	return nil
}

// 验证组合是否能删除所有数据
func validateCombination(data []RowData, conditions []FilterCondition) bool {
	currentData := make([]RowData, len(data))
	copy(currentData, data)

	for _, condition := range conditions {
		cols := getColumnsForCondition(condition)
		if len(cols) == 0 {
			return false
		}

		matchedRows := applyFilter(currentData, condition.Elements, cols)
		if len(matchedRows) == 0 {
			return false
		}

		currentData = removeRows(currentData, matchedRows)
	}

	return len(currentData) == 0
}

func main() {
	fmt.Println("=== 优化版大数据处理专家 - 重点关注四元组合 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 设置目标分数范围
	targetMin := 100.0
	targetMax := 105.0

	startTime := time.Now()

	// 智能搜索解决方案
	sequence := smartSearch(data, targetMin, targetMax)

	elapsed := time.Since(startTime)

	if sequence != nil {
		fmt.Printf("\n🎉 找到最优中奖序列！\n")
		fmt.Printf("总分: %.6f\n", sequence.TotalScore)
		fmt.Printf("序列长度: %d\n", len(sequence.Conditions))
		fmt.Printf("计算耗时: %v\n", elapsed)

		fmt.Println("\n中奖序列详情:")
		quadCount := 0
		singleCount := 0
		for i, condition := range sequence.Conditions {
			if condition.Type == "quad" {
				fmt.Printf("%d. 四元组合: %v = %.6f\n", i+1, condition.Elements, condition.Score)
				quadCount++
			} else {
				fmt.Printf("%d. 单元素: %s = %.6f\n", i+1, condition.Elements[0], condition.Score)
				singleCount++
			}
		}

		fmt.Printf("\n统计: %d个四元组合, %d个单元素\n", quadCount, singleCount)

	} else {
		fmt.Printf("\n❌ 未找到满足条件的中奖序列\n")
		fmt.Printf("计算耗时: %v\n", elapsed)

		// 尝试放宽条件
		fmt.Println("\n尝试放宽分数范围...")
		relaxedSequence := smartSearch(data, 95.0, 110.0)
		if relaxedSequence != nil {
			fmt.Printf("在放宽的范围内找到序列，总分: %.6f\n", relaxedSequence.TotalScore)
		}
	}
}
