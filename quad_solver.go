package main

import (
	"fmt"
	"log"
	"sort"
	"strings"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

type QuadCondition struct {
	Elements []string // 四个元素
	Columns  []int    // 对应的四个列
	Score    float64  // 得分
}

// 完整的元素分值映射
var ElementScores = map[string]float64{
	"az1": 2.29, "az2": 3.25, "az3": 2.60, "az4": 1.47, "az5": 4.05, "az6": 4.95,
	"bz1": 1.45, "bz2": 4.40, "bz3": 4.70, "bz4": 2.30, "bz5": 3.75, "bz6": 2.34,
	"cz1": 2.21, "cz2": 3.20, "cz3": 2.75, "cz4": 1.50, "cz5": 3.90, "cz6": 4.85,
	"dz1": 1.92, "dz2": 3.18, "dz3": 3.40, "dz4": 1.67, "dz5": 3.50, "dz6": 4.10,
	"aq0": 13.00, "aq1": 5.40, "aq2": 3.60, "aq3": 3.40, "aq4": 5.00, "aq5": 8.60, "aq6": 16.00, "aq7": 24.00,
	"bq0": 27.00, "bq1": 7.90, "bq2": 4.45, "bq3": 3.65, "bq4": 4.15, "bq5": 6.35, "bq6": 9.75, "bq7": 11.00,
	"cq0": 12.00, "cq1": 4.90, "cq2": 3.40, "cq3": 3.50, "cq4": 5.30, "cq5": 9.50, "cq6": 18.00, "cq7": 27.00,
	"dq0": 10.50, "dq1": 4.40, "dq2": 3.25, "dq3": 3.55, "dq4": 6.00, "dq5": 11.00, "dq6": 20.00, "dq7": 29.00,
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 计算四元组合得分
func calculateQuadScore(elements []string) float64 {
	product := 1.0
	for _, element := range elements {
		if score, exists := ElementScores[element]; exists {
			product *= score
		} else {
			return 0 // 如果任何元素不存在，返回0
		}
	}
	return 100.0 / product
}

// 生成四元组合
func generateQuadCombinations(data []RowData) []QuadCondition {
	var conditions []QuadCondition

	fmt.Println("正在生成四元组合...")

	// 获取各组特殊列的实际值
	groupValues := make(map[int][]string)

	// 特殊列：A(0), B(1), F(5), G(6), K(10), L(11), P(15), Q(16)
	specialCols := []int{0, 1, 5, 6, 10, 11, 15, 16}

	for _, col := range specialCols {
		valueMap := make(map[string]bool)
		for _, row := range data {
			valueMap[row.Values[col]] = true
		}

		var values []string
		for value := range valueMap {
			// 只保留在ElementScores中存在的值
			if _, exists := ElementScores[value]; exists {
				values = append(values, value)
			}
		}
		groupValues[col] = values
		fmt.Printf("列 %d: %d 个有效值\n", col, len(values))
	}

	// 生成四元组合：从四个不同组中各选一个特殊列
	// 第一组：A(0), B(1)
	// 第二组：F(5), G(6)
	// 第三组：K(10), L(11)
	// 第四组：P(15), Q(16)

	maxCombinations := 200
	count := 0

	for _, colA := range []int{0, 1} {
		for _, colB := range []int{5, 6} {
			for _, colC := range []int{10, 11} {
				for _, colD := range []int{15, 16} {
					if count >= maxCombinations {
						break
					}

					valuesA := groupValues[colA]
					valuesB := groupValues[colB]
					valuesC := groupValues[colC]
					valuesD := groupValues[colD]

					// 限制每列的值数量
					maxVals := 3
					if len(valuesA) > maxVals {
						valuesA = valuesA[:maxVals]
					}
					if len(valuesB) > maxVals {
						valuesB = valuesB[:maxVals]
					}
					if len(valuesC) > maxVals {
						valuesC = valuesC[:maxVals]
					}
					if len(valuesD) > maxVals {
						valuesD = valuesD[:maxVals]
					}

					for _, vA := range valuesA {
						for _, vB := range valuesB {
							for _, vC := range valuesC {
								for _, vD := range valuesD {
									if count >= maxCombinations {
										break
									}

									elements := []string{vA, vB, vC, vD}
									columns := []int{colA, colB, colC, colD}
									score := calculateQuadScore(elements)

									if score > 0 && score >= 0.5 && score <= 30 {
										conditions = append(conditions, QuadCondition{
											Elements: elements,
											Columns:  columns,
											Score:    score,
										})
										count++
									}
								}
								if count >= maxCombinations {
									break
								}
							}
							if count >= maxCombinations {
								break
							}
						}
						if count >= maxCombinations {
							break
						}
					}
				}
				if count >= maxCombinations {
					break
				}
			}
			if count >= maxCombinations {
				break
			}
		}
		if count >= maxCombinations {
			break
		}
	}

	fmt.Printf("生成了 %d 个四元组合\n", len(conditions))

	// 按得分排序
	sort.Slice(conditions, func(i, j int) bool {
		return conditions[i].Score > conditions[j].Score
	})

	return conditions
}

// 模拟四元组合筛选
func simulateQuadFiltering(data []RowData, condition QuadCondition) (int, int) {
	matchCount := 0
	for _, row := range data {
		match := true
		for i, element := range condition.Elements {
			if row.Values[condition.Columns[i]] != element {
				match = false
				break
			}
		}
		if match {
			matchCount++
		}
	}
	return matchCount, len(data) - matchCount
}

// 寻找四元组合解决方案
func findQuadSolution(data []RowData) {
	fmt.Println("=== 寻找四元组合解决方案 ===")

	conditions := generateQuadCombinations(data)

	targetMin := 100.0
	targetMax := 105.0

	fmt.Printf("\n测试四元组合（目标分数: %.1f - %.1f）...\n", targetMin, targetMax)

	// 测试单个四元组合
	fmt.Println("\n=== 单个四元组合测试 ===")
	for i, condition := range conditions {
		if i >= 20 { // 只测试前20个
			break
		}

		matchCount, remainCount := simulateQuadFiltering(data, condition)

		fmt.Printf("%d. %v\n", i+1, condition.Elements)
		fmt.Printf("   列: %v, 得分: %.6f\n", condition.Columns, condition.Score)
		fmt.Printf("   匹配: %d 行, 剩余: %d 行\n", matchCount, remainCount)

		if condition.Score >= targetMin && condition.Score <= targetMax {
			fmt.Printf("   ✅ 得分在目标范围内！\n")
		}
		fmt.Println()
	}

	// 测试多个四元组合的组合
	fmt.Println("\n=== 多个四元组合组合测试 ===")

	// 选择一些得分适中的四元组合进行组合测试
	var goodConditions []QuadCondition
	for _, condition := range conditions {
		if condition.Score >= 5 && condition.Score <= 50 {
			goodConditions = append(goodConditions, condition)
		}
		if len(goodConditions) >= 10 {
			break
		}
	}

	fmt.Printf("选择了 %d 个适中得分的四元组合进行组合测试\n", len(goodConditions))

	// 测试两个四元组合的组合
	for i := 0; i < len(goodConditions) && i < 5; i++ {
		for j := i + 1; j < len(goodConditions) && j < 5; j++ {
			totalScore := goodConditions[i].Score + goodConditions[j].Score

			if totalScore >= targetMin && totalScore <= targetMax {
				fmt.Printf("\n🎯 找到可能的解决方案:\n")
				fmt.Printf("组合1: %v (%.6f)\n", goodConditions[i].Elements, goodConditions[i].Score)
				fmt.Printf("组合2: %v (%.6f)\n", goodConditions[j].Elements, goodConditions[j].Score)
				fmt.Printf("总分: %.6f\n", totalScore)

				// 这里可以进一步验证这个组合是否能删除所有数据
				// 由于时间限制，暂时只显示理论上的解决方案
			}
		}
	}
}

func main() {
	fmt.Println("=== 四元组合解决方案搜索程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 寻找四元组合解决方案
	findQuadSolution(data)
}
