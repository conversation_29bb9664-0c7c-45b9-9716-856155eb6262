package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/xuri/excelize/v2"
)

// 数据结构定义
type RowData struct {
	Values [20]string // 20列数据
}

// 读取Excel数据
func readExcelData(filename string) ([]RowData, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var data []RowData
	for _, row := range rows {
		if len(row) < 20 {
			continue // 跳过不完整的行
		}

		var rowData RowData
		for i := 0; i < 20; i++ {
			rowData.Values[i] = strings.ToLower(strings.TrimSpace(row[i]))
		}
		data = append(data, rowData)
	}

	return data, nil
}

// 验证解决方案
func verifySolution(data []RowData, solution []string) {
	fmt.Printf("=== 验证解决方案: %v ===\n", solution)

	originalCount := len(data)
	fmt.Printf("原始数据行数: %d\n", originalCount)

	// 根据解决方案推断列索引
	// bz1 应该在F列(索引5), d11应该在R列(索引17)
	colMapping := map[string]int{
		"bz1": 5,  // F列
		"d11": 17, // R列
	}

	currentData := make([]RowData, len(data))
	copy(currentData, data)

	totalScore := 0.0

	for step, element := range solution {
		col, exists := colMapping[element]
		if !exists {
			fmt.Printf("❌ 无法确定元素 %s 对应的列\n", element)
			return
		}

		fmt.Printf("\n步骤 %d: 筛选 %s (列 %d)\n", step+1, element, col)

		// 统计匹配的行
		var matchedRows []int
		for i, row := range currentData {
			if row.Values[col] == element {
				matchedRows = append(matchedRows, i)
			}
		}

		fmt.Printf("匹配的行数: %d\n", len(matchedRows))

		if len(matchedRows) == 0 {
			fmt.Printf("❌ 没有找到匹配 %s 的行\n", element)
			return
		}

		// 移除匹配的行
		var newData []RowData
		removeMap := make(map[int]bool)
		for _, idx := range matchedRows {
			removeMap[idx] = true
		}

		for i, row := range currentData {
			if !removeMap[i] {
				newData = append(newData, row)
			}
		}

		currentData = newData

		// 计算得分
		var elementScore float64
		switch element {
		case "bz1":
			elementScore = 100.0 / 1.45 // 68.965517
		case "d11":
			elementScore = 100.0 / 3.05 // 32.786885
		}

		totalScore += elementScore

		fmt.Printf("删除 %d 行，剩余 %d 行\n", len(matchedRows), len(currentData))
		fmt.Printf("当前得分: %.6f\n", totalScore)
	}

	fmt.Printf("\n=== 最终结果 ===\n")
	fmt.Printf("总得分: %.6f\n", totalScore)
	fmt.Printf("剩余行数: %d\n", len(currentData))

	if len(currentData) == 0 {
		if totalScore >= 100.0 && totalScore <= 105.0 {
			fmt.Printf("🎉 解决方案验证成功！所有数据被删除，得分在目标范围内！\n")
		} else {
			fmt.Printf("⚠️  所有数据被删除，但得分不在目标范围内 (100-105)\n")
		}
	} else {
		fmt.Printf("❌ 解决方案不完整，还有 %d 行数据未被删除\n", len(currentData))

		// 显示前几行剩余数据作为参考
		fmt.Println("\n前5行剩余数据:")
		for i := 0; i < 5 && i < len(currentData); i++ {
			fmt.Printf("行 %d: %v\n", i+1, currentData[i].Values)
		}
	}
}

// 分析数据中的元素分布
func analyzeData(data []RowData) {
	fmt.Println("=== 数据分析 ===")

	// 分析F列(索引5)中bz1的分布
	bz1Count := 0
	for _, row := range data {
		if row.Values[5] == "bz1" {
			bz1Count++
		}
	}
	fmt.Printf("F列中 bz1 的行数: %d\n", bz1Count)

	// 分析R列(索引17)中d11的分布
	d11Count := 0
	for _, row := range data {
		if row.Values[17] == "d11" {
			d11Count++
		}
	}
	fmt.Printf("R列中 d11 的行数: %d\n", d11Count)

	// 分析同时包含bz1和d11的行数
	bothCount := 0
	for _, row := range data {
		if row.Values[5] == "bz1" && row.Values[17] == "d11" {
			bothCount++
		}
	}
	fmt.Printf("同时包含 bz1 和 d11 的行数: %d\n", bothCount)

	// 显示F列和R列的所有唯一值
	fValues := make(map[string]int)
	rValues := make(map[string]int)

	for _, row := range data {
		fValues[row.Values[5]]++
		rValues[row.Values[17]]++
	}

	fmt.Printf("\nF列的唯一值 (%d个):\n", len(fValues))
	for value, count := range fValues {
		fmt.Printf("  %s: %d行\n", value, count)
	}

	fmt.Printf("\nR列的唯一值 (%d个):\n", len(rValues))
	for value, count := range rValues {
		fmt.Printf("  %s: %d行\n", value, count)
	}
}

func main() {
	fmt.Println("=== 解决方案验证程序 ===")

	// 读取Excel数据
	fmt.Println("正在读取Excel文件...")
	data, err := readExcelData("data.xlsx")
	if err != nil {
		log.Fatalf("读取Excel文件失败: %v", err)
	}

	fmt.Printf("成功读取 %d 行数据\n", len(data))

	// 分析数据
	analyzeData(data)

	// 验证理论解决方案
	solution := []string{"bz1", "d11"}
	verifySolution(data, solution)
}
